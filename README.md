# Discord Bot with Next.js 15 & TypeScript

A comprehensive Discord bot built with Next.js 15 and TypeScript featuring advanced event logging and shopping list compilation capabilities.

## 🚀 Features

### Event & Channel Logging
- **Complete Event Tracking**: Monitors message events (create, edit, delete) and channel events (create, update, delete)
- **Configurable Logging**: Register specific source channels to be logged to designated output channels
- **Rich Embeds**: Beautiful, informative log messages with timestamps, user info, and change details
- **Flexible Management**: Easy setup and removal of logging configurations per server

### Shopping List Compilation
- **Smart Compilation**: Automatically compiles shopping items from a designated channel
- **Duplicate Removal**: Intelligently removes duplicate items (case-insensitive)
- **Contributor Tracking**: Shows who added items and how many
- **Time-based**: Only includes items added since the last compilation
- **User-friendly Output**: Clean, organized shopping lists posted to your designated channel

## 📋 Prerequisites

- Node.js 18+ installed
- A Discord application with a bot token
- Basic understanding of Discord server management

## 🔧 Installation & Setup

### 1. <PERSON>lone and Install Dependencies

```bash
git clone <your-repo-url>
cd discord-bot-nextjs
npm install
```

### 2. Create Discord Application

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name
3. Go to the "Bot" section in the sidebar
4. Click "Add Bot"
5. Copy the bot token (you'll need this later)
6. Under "Privileged Gateway Intents", enable:
   - Server Members Intent
   - Message Content Intent

### 3. Get Application ID

1. In your Discord application, go to "General Information"
2. Copy the "Application ID" (this is your Client ID)

### 4. Environment Configuration

1. Copy `.env.example` to `.env.local`:
```bash
cp .env.example .env.local
```

2. Fill in your environment variables:
```env
DISCORD_BOT_TOKEN=your_bot_token_here
DISCORD_CLIENT_ID=your_application_id_here
DISCORD_GUILD_ID=your_server_id_here_optional
```

**Finding your Guild ID (Server ID):**
1. Enable Developer Mode in Discord (User Settings → Advanced → Developer Mode)
2. Right-click your server name and select "Copy Server ID"

### 5. Invite Bot to Your Server

Create an invite link with the necessary permissions:

```
https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=2147483648&scope=bot%20applications.commands
```

Replace `YOUR_CLIENT_ID` with your actual Client ID.

**Required Permissions:**
- Read Messages/View Channels
- Send Messages
- Use Slash Commands
- Embed Links
- Read Message History
- Manage Messages (for logging deleted messages)

### 6. Create Required Channels (for Shopping Lists)

In your Discord server, create these channels:
- `#shopping-list-items` - Where users add items
- `#shopping-lists` - Where compiled lists are posted

## 🎮 Running the Bot

### Method 1: Using the Dashboard (Recommended)

1. Start the Next.js development server:
```bash
npm run dev
```

2. Open http://localhost:3000 in your browser
3. Use the dashboard to start the bot and deploy commands

### Method 2: Direct Command Line

1. Deploy slash commands:
```bash
npm run bot:start
```

This will automatically deploy commands and start the bot.

### Method 3: Development Mode

For development with auto-restart:
```bash
npm run bot:dev
```

## 📝 Bot Commands

### Logging Commands

#### `/register-logging`
Register a source channel to be logged to an output channel.
- **source**: The channel you want to monitor
- **output**: The channel where logs will be posted

**Example**: `/register-logging source:#general output:#mod-logs`

#### `/unregister-logging`
Stop logging a previously registered channel.
- **source**: The channel to stop monitoring

**Example**: `/unregister-logging source:#general`

#### `/list-logged-channels`
Display all currently registered logging configurations.

### Shopping List Commands

#### `/compile-shopping`
Compile all items added to #shopping-list-items since the last compilation.

**Usage:**
1. Users add items to #shopping-list-items channel
2. Run `/compile-shopping` in any channel
3. Bot posts compiled list with contributors and item counts

## 🗂️ Project Structure

```
├── app/
│   ├── api/bot/route.ts          # Bot control API
│   ├── page.tsx                  # Dashboard UI
│   └── layout.tsx                # Root layout
├── lib/
│   ├── discord/
│   │   ├── client.ts             # Main bot client
│   │   ├── logger.ts             # Event logging system
│   │   └── shopping.ts           # Shopping list compilation
│   └── storage/
│       ├── config.ts             # Channel configuration storage
│       └── shopping.ts           # Shopping list state storage
├── scripts/
│   └── start-bot.ts              # Bot startup script
├── data/                         # Generated data storage
│   ├── config/                   # Channel configurations
│   └── shopping/                 # Shopping list states
└── README.md
```

## 🔧 Configuration

### Data Storage

The bot uses JSON files for data persistence:
- `data/config/` - Stores channel logging configurations per server
- `data/shopping/` - Stores shopping list compilation timestamps per server

For production deployments, consider migrating to a database like PostgreSQL or MongoDB.

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `DISCORD_BOT_TOKEN` | ✅ | Your Discord bot token |
| `DISCORD_CLIENT_ID` | ✅ | Your Discord application ID |
| `DISCORD_GUILD_ID` | ❌ | Specific server ID (for guild-only commands) |

## 🚀 Deployment

### Railway

1. Fork this repository
2. Connect to Railway
3. Add environment variables
4. Deploy

### Heroku

1. Install Heroku CLI
2. Create new app: `heroku create your-bot-name`
3. Set environment variables:
```bash
heroku config:set DISCORD_BOT_TOKEN=your_token_here
heroku config:set DISCORD_CLIENT_ID=your_client_id_here
```
4. Deploy: `git push heroku main`

### Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🛠️ Troubleshooting

### Common Issues

**Bot doesn't respond to commands:**
- Ensure bot has "Use Slash Commands" permission
- Verify commands are deployed (`/deploy-commands`)
- Check bot token is correct

**Logging not working:**
- Verify bot can read messages in source channel
- Ensure bot can send messages in output channel
- Check channel IDs are registered correctly

**Shopping list compilation fails:**
- Create #shopping-list-items channel
- Ensure bot has message history permissions
- Verify users are adding items to the correct channel

### Debug Mode

Enable additional logging by setting:
```env
NODE_ENV=development
```

## 📊 Monitoring

The dashboard at http://localhost:3000 provides:
- Real-time bot status
- Uptime monitoring
- Command deployment tools
- Feature overview

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Discord Developer Portal for permission issues
3. Check console logs for error messages
4. Create an issue in this repository with detailed information

## 🎯 Future Enhancements

- Database integration (PostgreSQL/MongoDB)
- Advanced logging filters
- Shopping list categories and priorities
- Web dashboard for configuration
- Multi-server support improvements
- Automated backups
- Custom embed styling
- Role-based permissions

---

Built with ❤️ using Next.js 15, TypeScript, and Discord.js