import { promises as fs } from 'fs';
import path from 'path';

export interface ShoppingState {
  lastCompiled: number; // timestamp
}

const SHOPPING_DIR = path.join(process.cwd(), 'data', 'shopping');

export async function loadShoppingState(guildId: string): Promise<ShoppingState> {
  try {
    await fs.mkdir(SHOPPING_DIR, { recursive: true });
    const statePath = path.join(SHOPPING_DIR, `${guildId}.json`);
    
    try {
      const data = await fs.readFile(statePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      // File doesn't exist, return default state
      return { lastCompiled: 0 };
    }
  } catch (error) {
    console.error('Error loading shopping state:', error);
    return { lastCompiled: 0 };
  }
}

export async function saveShoppingState(guildId: string, state: ShoppingState): Promise<void> {
  try {
    await fs.mkdir(SHOPPING_DIR, { recursive: true });
    const statePath = path.join(SHOPPING_DIR, `${guildId}.json`);
    await fs.writeFile(statePath, JSON.stringify(state, null, 2));
  } catch (error) {
    console.error('Error saving shopping state:', error);
    throw error;
  }
}