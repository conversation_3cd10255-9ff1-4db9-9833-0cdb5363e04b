import { Client, EmbedBuilder, TextChannel, Message, Channel, User, GuildChannel } from 'discord.js';
import { loadChannelConfig } from '../storage/config';

export async function logEvent(
  guildId: string,
  eventType: string,
  data: any,
  client: Client,
  oldData?: any
) {
  try {
    const config = await loadChannelConfig(guildId);
    
    // Check if this channel should be logged
    const sourceChannelId = data.channelId || data.id;
    const outputChannelId = config.loggedChannels[sourceChannelId];
    
    if (!outputChannelId) return;

    const outputChannel = await client.channels.fetch(outputChannelId).catch(() => null);
    if (!outputChannel || !outputChannel.isTextBased()) return;

    const embed = createLogEmbed(eventType, data, oldData);
    await (outputChannel as TextChannel).send({ embeds: [embed] });
  } catch (error) {
    console.error('Error logging event:', error);
  }
}

function createLogEmbed(eventType: string, data: any, oldData?: any): EmbedBuilder {
  const embed = new EmbedBuilder()
    .setTimestamp()
    .setFooter({ text: `Event: ${eventType}` });

  switch (eventType) {
    case 'MESSAGE_CREATE':
      return embed
        .setTitle('📝 Message Created')
        .setColor(0x00FF00)
        .addFields(
          { name: '👤 User', value: `${data.author.tag} (${data.author.id})`, inline: true },
          { name: '📍 Channel', value: `<#${data.channelId}>`, inline: true },
          { name: '💬 Content', value: data.content || '*No content*', inline: false },
          { name: '🔗 Jump Link', value: `[Go to message](${data.url})`, inline: true }
        );

    case 'MESSAGE_DELETE':
      return embed
        .setTitle('🗑️ Message Deleted')
        .setColor(0xFF0000)
        .addFields(
          { name: '👤 User', value: data.author ? `${data.author.tag} (${data.author.id})` : 'Unknown', inline: true },
          { name: '📍 Channel', value: `<#${data.channelId}>`, inline: true },
          { name: '💬 Content', value: data.content || '*No content*', inline: false }
        );

    case 'MESSAGE_UPDATE':
      return embed
        .setTitle('✏️ Message Edited')
        .setColor(0xFFFF00)
        .addFields(
          { name: '👤 User', value: `${data.author.tag} (${data.author.id})`, inline: true },
          { name: '📍 Channel', value: `<#${data.channelId}>`, inline: true },
          { name: '📝 Old Content', value: oldData?.content || '*No content*', inline: false },
          { name: '📝 New Content', value: data.content || '*No content*', inline: false },
          { name: '🔗 Jump Link', value: `[Go to message](${data.url})`, inline: true }
        );

    case 'CHANNEL_CREATE':
      return embed
        .setTitle('🆕 Channel Created')
        .setColor(0x00FF00)
        .addFields(
          { name: '📍 Channel', value: `${data.name} (<#${data.id}>)`, inline: true },
          { name: '📂 Type', value: getChannelTypeName(data.type), inline: true },
          { name: '📄 Topic', value: data.topic || '*No topic*', inline: false }
        );

    case 'CHANNEL_DELETE':
      return embed
        .setTitle('🗑️ Channel Deleted')
        .setColor(0xFF0000)
        .addFields(
          { name: '📍 Channel', value: `${data.name} (${data.id})`, inline: true },
          { name: '📂 Type', value: getChannelTypeName(data.type), inline: true },
          { name: '📄 Topic', value: data.topic || '*No topic*', inline: false }
        );

    case 'CHANNEL_UPDATE':
      const changes = getChannelChanges(oldData, data);
      return embed
        .setTitle('✏️ Channel Updated')
        .setColor(0xFFFF00)
        .addFields(
          { name: '📍 Channel', value: `${data.name} (<#${data.id}>)`, inline: true },
          { name: '📂 Type', value: getChannelTypeName(data.type), inline: true },
          { name: '🔄 Changes', value: changes, inline: false }
        );

    default:
      return embed
        .setTitle('📊 Unknown Event')
        .setColor(0x808080)
        .setDescription(`Event type: ${eventType}`);
  }
}

function getChannelTypeName(type: number): string {
  const types: { [key: number]: string } = {
    0: 'Text Channel',
    1: 'DM',
    2: 'Voice Channel',
    3: 'Group DM',
    4: 'Category',
    5: 'Announcement Channel',
    10: 'Announcement Thread',
    11: 'Public Thread',
    12: 'Private Thread',
    13: 'Stage Channel',
    14: 'Directory',
    15: 'Forum Channel'
  };
  return types[type] || `Unknown (${type})`;
}

function getChannelChanges(oldChannel: any, newChannel: any): string {
  const changes: string[] = [];

  if (oldChannel.name !== newChannel.name) {
    changes.push(`**Name:** ${oldChannel.name} → ${newChannel.name}`);
  }

  if (oldChannel.topic !== newChannel.topic) {
    changes.push(`**Topic:** ${oldChannel.topic || '*None*'} → ${newChannel.topic || '*None*'}`);
  }

  if (oldChannel.position !== newChannel.position) {
    changes.push(`**Position:** ${oldChannel.position} → ${newChannel.position}`);
  }

  return changes.length > 0 ? changes.join('\n') : 'No significant changes detected';
}