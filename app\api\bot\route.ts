import { NextRequest, NextResponse } from 'next/server';
import DiscordBot from '@/lib/discord/client';

let bot: DiscordBot | null = null;

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    if (action === 'start') {
      if (bot) {
        return NextResponse.json({ error: 'Bo<PERSON> is already running' }, { status: 400 });
      }

      const token = process.env.DISCORD_BOT_TOKEN;
      if (!token) {
        return NextResponse.json({ error: 'DISCORD_BOT_TOKEN not found' }, { status: 500 });
      }

      bot = new DiscordBot();
      await bot.start(token);

      return NextResponse.json({ message: 'Bo<PERSON> started successfully' });
    }

    if (action === 'deploy-commands') {
      const token = process.env.DISCORD_BOT_TOKEN;
      const clientId = process.env.DISCORD_CLIENT_ID;
      const guildId = process.env.DISCORD_GUILD_ID; // Optional, for guild-specific commands

      if (!token || !clientId) {
        return NextResponse.json({ error: 'Missing environment variables' }, { status: 500 });
      }

      if (!bot) {
        bot = new DiscordBot();
      }

      await bot.deployCommands(token, clientId, guildId);

      return NextResponse.json({ message: 'Commands deployed successfully' });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Bot API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    status: bot ? 'running' : 'stopped',
    uptime: bot ? process.uptime() : 0
  });
}