import { promises as fs } from 'fs';
import path from 'path';

export interface ChannelConfig {
  loggedChannels: { [sourceChannelId: string]: string }; // sourceId -> outputId
}

const CONFIG_DIR = path.join(process.cwd(), 'data', 'config');

export async function loadChannelConfig(guildId: string): Promise<ChannelConfig> {
  try {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
    const configPath = path.join(CONFIG_DIR, `${guildId}.json`);
    
    try {
      const data = await fs.readFile(configPath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      // File doesn't exist, return default config
      return { loggedChannels: {} };
    }
  } catch (error) {
    console.error('Error loading channel config:', error);
    return { loggedChannels: {} };
  }
}

export async function saveChannelConfig(guildId: string, config: ChannelConfig): Promise<void> {
  try {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
    const configPath = path.join(CONFIG_DIR, `${guildId}.json`);
    await fs.writeFile(configPath, JSON.stringify(config, null, 2));
  } catch (error) {
    console.error('Error saving channel config:', error);
    throw error;
  }
}