import { Client, GatewayIntentBits, Events, Message, TextChannel, EmbedBuilder, SlashCommandBuilder, Collection, CommandInteraction } from 'discord.js';
import { logEvent } from './logger';
import { compileShoppingList } from './shopping';
import { loadChannelConfig, saveChannelConfig, ChannelConfig } from '../storage/config';

interface Command {
  data: SlashCommandBuilder;
  execute: (interaction: CommandInteraction) => Promise<void>;
}

class DiscordBot {
  private client: Client;
  private commands: Collection<string, Command>;

  constructor() {
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildPresences
      ]
    });

    this.commands = new Collection();
    this.setupCommands();
    this.setupEventListeners();
  }

  private setupCommands() {
    const commands: Command[] = [
      {
        data: new SlashCommandBuilder()
          .setName('register-logging')
          .setDescription('Register a source channel for logging to an output channel')
          .addChannelOption(option =>
            option.setName('source')
              .setDescription('The channel to monitor')
              .setRequired(true))
          .addChannelOption(option =>
            option.setName('output')
              .setDescription('The channel where logs will be posted')
              .setRequired(true)),
        execute: async (interaction: CommandInteraction) => {
          const sourceChannel = interaction.options.get('source')?.channel;
          const outputChannel = interaction.options.get('output')?.channel;

          if (!sourceChannel || !outputChannel || !interaction.guildId) {
            await interaction.reply('Invalid channels specified.');
            return;
          }

          const config = await loadChannelConfig(interaction.guildId);
          config.loggedChannels[sourceChannel.id] = outputChannel.id;
          await saveChannelConfig(interaction.guildId, config);

          await interaction.reply(`✅ Now logging events from ${sourceChannel} to ${outputChannel}`);
        }
      },
      {
        data: new SlashCommandBuilder()
          .setName('unregister-logging')
          .setDescription('Stop logging a channel')
          .addChannelOption(option =>
            option.setName('source')
              .setDescription('The channel to stop monitoring')
              .setRequired(true)),
        execute: async (interaction: CommandInteraction) => {
          const sourceChannel = interaction.options.get('source')?.channel;

          if (!sourceChannel || !interaction.guildId) {
            await interaction.reply('Invalid channel specified.');
            return;
          }

          const config = await loadChannelConfig(interaction.guildId);
          delete config.loggedChannels[sourceChannel.id];
          await saveChannelConfig(interaction.guildId, config);

          await interaction.reply(`✅ Stopped logging events from ${sourceChannel}`);
        }
      },
      {
        data: new SlashCommandBuilder()
          .setName('compile-shopping')
          .setDescription('Compile shopping list from items channel'),
        execute: async (interaction: CommandInteraction) => {
          if (!interaction.guildId) {
            await interaction.reply('This command can only be used in a server.');
            return;
          }

          await interaction.deferReply();

          try {
            const result = await compileShoppingList(interaction.guildId, this.client, interaction.channelId);
            await interaction.editReply(result);
          } catch (error) {
            console.error('Error compiling shopping list:', error);
            await interaction.editReply('❌ Failed to compile shopping list. Please try again.');
          }
        }
      },
      {
        data: new SlashCommandBuilder()
          .setName('list-logged-channels')
          .setDescription('Show all currently logged channels'),
        execute: async (interaction: CommandInteraction) => {
          if (!interaction.guildId) {
            await interaction.reply('This command can only be used in a server.');
            return;
          }

          const config = await loadChannelConfig(interaction.guildId);
          const loggedChannels = Object.entries(config.loggedChannels);

          if (loggedChannels.length === 0) {
            await interaction.reply('No channels are currently being logged.');
            return;
          }

          const embed = new EmbedBuilder()
            .setTitle('📋 Logged Channels')
            .setColor(0x0099FF)
            .setTimestamp();

          let description = '';
          for (const [sourceId, outputId] of loggedChannels) {
            const sourceChannel = await this.client.channels.fetch(sourceId).catch(() => null);
            const outputChannel = await this.client.channels.fetch(outputId).catch(() => null);
            
            description += `📍 **Source:** ${sourceChannel || `<#${sourceId}> (deleted)`}\n`;
            description += `📤 **Output:** ${outputChannel || `<#${outputId}> (deleted)`}\n\n`;
          }

          embed.setDescription(description);
          await interaction.reply({ embeds: [embed] });
        }
      }
    ];

    commands.forEach(command => {
      this.commands.set(command.data.name, command);
    });
  }

  private setupEventListeners() {
    this.client.once(Events.ClientReady, () => {
      console.log(`✅ Discord bot is ready! Logged in as ${this.client.user?.tag}`);
    });

    this.client.on(Events.InteractionCreate, async interaction => {
      if (!interaction.isChatInputCommand()) return;

      const command = this.commands.get(interaction.commandName);
      if (!command) return;

      try {
        await command.execute(interaction);
      } catch (error) {
        console.error('Error executing command:', error);
        const reply = { content: 'There was an error while executing this command!', ephemeral: true };
        
        if (interaction.replied || interaction.deferred) {
          await interaction.followUp(reply);
        } else {
          await interaction.reply(reply);
        }
      }
    });

    // Message events for logging
    this.client.on(Events.MessageCreate, async (message: Message) => {
      if (message.author.bot) return;
      await logEvent(message.guildId!, 'MESSAGE_CREATE', message, this.client);
    });

    this.client.on(Events.MessageDelete, async (message: Message) => {
      await logEvent(message.guildId!, 'MESSAGE_DELETE', message, this.client);
    });

    this.client.on(Events.MessageUpdate, async (oldMessage: Message, newMessage: Message) => {
      await logEvent(newMessage.guildId!, 'MESSAGE_UPDATE', newMessage, this.client, oldMessage);
    });

    // Channel events
    this.client.on(Events.ChannelCreate, async (channel) => {
      if (!('guild' in channel) || !channel.guild) return;
      await logEvent(channel.guild.id, 'CHANNEL_CREATE', channel, this.client);
    });

    this.client.on(Events.ChannelDelete, async (channel) => {
      if (!('guild' in channel) || !channel.guild) return;
      await logEvent(channel.guild.id, 'CHANNEL_DELETE', channel, this.client);
    });

    this.client.on(Events.ChannelUpdate, async (oldChannel, newChannel) => {
      if (!('guild' in newChannel) || !newChannel.guild) return;
      await logEvent(newChannel.guild.id, 'CHANNEL_UPDATE', newChannel, this.client, oldChannel);
    });
  }

  async start(token: string) {
    try {
      await this.client.login(token);
    } catch (error) {
      console.error('Failed to start Discord bot:', error);
      throw error;
    }
  }

  async deployCommands(token: string, clientId: string, guildId?: string) {
    const { REST } = await import('@discordjs/rest');
    const { Routes } = await import('discord-api-types/v10');

    const commands = Array.from(this.commands.values()).map(command => command.data.toJSON());

    const rest = new REST({ version: '10' }).setToken(token);

    try {
      console.log('Started refreshing application (/) commands.');

      const route = guildId 
        ? Routes.applicationGuildCommands(clientId, guildId)
        : Routes.applicationCommands(clientId);

      await rest.put(route, { body: commands });

      console.log('Successfully reloaded application (/) commands.');
    } catch (error) {
      console.error('Error deploying commands:', error);
      throw error;
    }
  }

  getClient() {
    return this.client;
  }
}

export default DiscordBot;