import DiscordBot from '../lib/discord/client';

async function startBot() {
  const token = process.env.DISCORD_BOT_TOKEN;
  const clientId = process.env.DISCORD_CLIENT_ID;
  const guildId = process.env.DISCORD_GUILD_ID; // Optional

  if (!token) {
    console.error('❌ DISCORD_BOT_TOKEN environment variable is required');
    process.exit(1);
  }

  if (!clientId) {
    console.error('❌ DISCORD_CLIENT_ID environment variable is required');
    process.exit(1);
  }

  try {
    const bot = new DiscordBot();
    
    // Deploy commands first
    console.log('🚀 Deploying slash commands...');
    await bot.deployCommands(token, clientId, guildId);
    
    // Start the bot
    console.log('🤖 Starting Discord bot...');
    await bot.start(token);
    
    console.log('✅ Bot is now running!');
  } catch (error) {
    console.error('❌ Failed to start bot:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down bot...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down bot...');
  process.exit(0);
});

startBot();