'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Bot, MessageSquare, ShoppingCart, Settings, CheckCircle, XCircle } from 'lucide-react';

interface BotStatus {
  status: 'running' | 'stopped';
  uptime: number;
}

export default function Home() {
  const [botStatus, setBotStatus] = useState<BotStatus>({ status: 'stopped', uptime: 0 });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    checkBotStatus();
    const interval = setInterval(checkBotStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  const checkBotStatus = async () => {
    try {
      const response = await fetch('/api/bot');
      const data = await response.json();
      setBotStatus(data);
    } catch (error) {
      console.error('Failed to check bot status:', error);
    }
  };

  const handleBotAction = async (action: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/bot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log(data.message);
        await checkBotStatus();
      } else {
        console.error(data.error);
      }
    } catch (error) {
      console.error('Failed to perform bot action:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours}h ${minutes}m ${secs}s`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Bot className="w-12 h-12 text-blue-600" />
            <h1 className="text-4xl font-bold text-slate-900">Discord Bot Dashboard</h1>
          </div>
          <p className="text-slate-600 text-lg">
            Advanced Discord bot with event logging and shopping list compilation
          </p>
        </div>

        <div className="grid gap-6 mb-8">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Bot Status
                  </CardTitle>
                  <CardDescription>Current bot status and controls</CardDescription>
                </div>
                <Badge 
                  variant={botStatus.status === 'running' ? 'default' : 'destructive'}
                  className="flex items-center gap-2"
                >
                  {botStatus.status === 'running' ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    <XCircle className="w-4 h-4" />
                  )}
                  {botStatus.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  {botStatus.status === 'running' && (
                    <p className="text-sm text-slate-600">
                      Uptime: {formatUptime(botStatus.uptime)}
                    </p>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleBotAction('start')}
                    disabled={loading || botStatus.status === 'running'}
                    size="sm"
                  >
                    Start Bot
                  </Button>
                  <Button
                    onClick={() => handleBotAction('deploy-commands')}
                    disabled={loading}
                    variant="outline"
                    size="sm"
                  >
                    Deploy Commands
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-blue-600" />
                  Event & Channel Logging
                </CardTitle>
                <CardDescription>
                  Comprehensive event logging system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Message events (create, edit, delete)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Channel events (create, update, delete)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Configurable source/output channels</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Rich embed logging with timestamps</span>
                  </div>
                </div>
                <Separator className="my-4" />
                <div className="text-sm text-slate-600">
                  <strong>Commands:</strong><br />
                  • <code>/register-logging</code> - Set up channel logging<br />
                  • <code>/unregister-logging</code> - Stop channel logging<br />
                  • <code>/list-logged-channels</code> - View active logs
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5 text-green-600" />
                  Shopping List Compilation
                </CardTitle>
                <CardDescription>
                  Automated shopping list management
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Tracks items from #shopping-list-items</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Compiles since last compilation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Groups by contributors</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Removes duplicates automatically</span>
                  </div>
                </div>
                <Separator className="my-4" />
                <div className="text-sm text-slate-600">
                  <strong>Usage:</strong><br />
                  1. Create #shopping-list-items channel<br />
                  2. Users add items to that channel<br />
                  3. Use <code>/compile-shopping</code> to generate list
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Quick Setup Guide</CardTitle>
            <CardDescription>
              Follow these steps to get your bot running
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">1. Environment Setup</h4>
                <p className="text-sm text-slate-600 mb-3">
                  Copy <code>.env.example</code> to <code>.env.local</code> and fill in your Discord bot credentials.
                </p>
                
                <h4 className="font-semibold mb-2">2. Required Channels</h4>
                <p className="text-sm text-slate-600">
                  For shopping lists, create <code>#shopping-list-items</code> and <code>#shopping-lists</code> channels in your Discord server.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">3. Bot Permissions</h4>
                <p className="text-sm text-slate-600 mb-3">
                  Ensure your bot has permissions to read messages, send messages, and manage channels.
                </p>
                
                <h4 className="font-semibold mb-2">4. Start the Bot</h4>
                <p className="text-sm text-slate-600">
                  Use the controls above or run <code>npm run bot:start</code> in your terminal.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}