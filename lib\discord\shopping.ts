import { Client, TextChannel, Message } from 'discord.js';
import { loadShoppingState, saveShoppingState } from '../storage/shopping';

export async function compileShoppingList(
  guildId: string,
  client: Client,
  commandChannelId: string
): Promise<string> {
  try {
    // Find the shopping-list-items channel
    const guild = await client.guilds.fetch(guildId);
    const itemsChannel = guild.channels.cache.find(
      channel => channel.name === 'shopping-list-items' && channel.isTextBased()
    ) as TextChannel;

    if (!itemsChannel) {
      return '❌ Could not find #shopping-list-items channel. Please create this channel first.';
    }

    // Load the last compilation timestamp
    const shoppingState = await loadShoppingState(guildId);
    const lastCompiled = shoppingState.lastCompiled;

    // Fetch messages since last compilation
    const messages: Message[] = [];
    let lastMessageId: string | undefined;

    // Fetch messages in batches
    while (true) {
      const fetchOptions: any = { limit: 100 };
      if (lastMessageId) {
        fetchOptions.before = lastMessageId;
      }

      const batch = await itemsChannel.messages.fetch(fetchOptions);
      if (batch.size === 0) break;

      const filteredBatch = batch.filter(msg => {
        return !msg.author.bot && 
               msg.createdTimestamp > lastCompiled &&
               msg.content.trim().length > 0;
      });

      messages.push(...filteredBatch.values());
      lastMessageId = batch.last()?.id;

      // If we got less than 100 messages, we've reached the end
      if (batch.size < 100) break;
    }

    // Sort messages by creation time (oldest first)
    messages.sort((a, b) => a.createdTimestamp - b.createdTimestamp);

    if (messages.length === 0) {
      return '📝 No new items have been added since the last compilation.';
    }

    // Group items by user
    const itemsByUser = new Map<string, string[]>();
    
    messages.forEach(message => {
      const userTag = message.author.tag;
      const items = message.content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      if (!itemsByUser.has(userTag)) {
        itemsByUser.set(userTag, []);
      }
      itemsByUser.get(userTag)!.push(...items);
    });

    // Create the compiled list
    let compiledList = '🛒 **Shopping List Compilation**\n';
    compiledList += `*Items added since: ${new Date(lastCompiled).toLocaleString()}*\n\n`;

    // Add all unique items
    const allItems = new Set<string>();
    itemsByUser.forEach((items) => {
      items.forEach(item => allItems.add(item.toLowerCase()));
    });

    if (allItems.size > 0) {
      compiledList += '**📋 Items to buy:**\n';
      Array.from(allItems)
        .sort()
        .forEach((item, index) => {
          compiledList += `${index + 1}. ${item}\n`;
        });
    }

    compiledList += '\n**👥 Contributors:**\n';
    itemsByUser.forEach((items, user) => {
      compiledList += `• ${user}: ${items.length} item${items.length !== 1 ? 's' : ''}\n`;
    });

    compiledList += `\n*Total: ${allItems.size} unique item${allItems.size !== 1 ? 's' : ''}*`;

    // Update the last compilation timestamp
    shoppingState.lastCompiled = Date.now();
    await saveShoppingState(guildId, shoppingState);

    // If the message is too long, truncate it
    if (compiledList.length > 2000) {
      const truncated = compiledList.substring(0, 1950);
      compiledList = truncated + '\n\n*... (message truncated)*';
    }

    return compiledList;
  } catch (error) {
    console.error('Error compiling shopping list:', error);
    throw error;
  }
}